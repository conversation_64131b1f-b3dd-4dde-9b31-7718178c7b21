import React, { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';
import { Save, ArrowRight } from 'lucide-react';

const PremiumAnalysisForm = () => {
  // Initialize form state
  const [formData, setFormData] = useState({
    currentPremium: '',
    changePremium: false,
    singlePayPremium: '',
    singlePayPaidInYear: '',
    limitedPayPremium: '',
    limitedPayYears: '',
    changeYearToYear: false,
    continueScheduled: true,
    currentCreditingRate: '',
    guaranteedMinRate: '',
    excessEarningsRate: '',
    includeRiderChanges: false,
    stopPremiumPayments: false,
    stopAge: '',
    stopYear: '',
    stopOtherAge: '',
    stopCashValue: '',
    modelLevelPremium: false,
    levelPremiumPercentage: '',
    levelPremiumAmount: '',
    levelPremiumGoal: '',
    levelPremiumYears: '',
    levelPremiumAge: '',
    levelPremiumMaximize: false,
    levelPremiumEndAge: '',
    noLevelPremium: true,
    catchUpPremium: false,
    catchUpAmount: '',
    catchUpYears: '',
    catchUpOverYears: '',
    catchUpStartPolicyYear: '',
    catchUpStartAge: '',
    catchUpTarget: '',
    catchUpCashValue: '',
    noCatchUp: true
  });

  // Dashboard context for tab and customer selection
  const { setActiveTab, selectedCustomerData, selectedPolicyData } = useDashboard();

  // Analysis results state
  type ProjectionData = {
    year: number;
    basePremium: number;
    modifiedPremium: number;
    baseCashValue: number;
    modifiedCashValue: number;
  };
  const [analysisResults, setAnalysisResults] = useState<ProjectionData[] | null>(null);
  const [showReport, setShowReport] = useState(false);

  // Handle form input changes
  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Generate sample projection data
  const generateProjectionData = () => {
    const years = Array.from({ length: 20 }, (_, i) => i + 1);
    const currentPremium = parseFloat(formData.currentPremium) || 5000;
    
    return years.map(year => {
      let basePremium = currentPremium;
      let modifiedPremium = currentPremium;
      let baseCashValue = currentPremium * year * 0.8;
      let modifiedCashValue = currentPremium * year * 0.8;

      // Apply single pay premium
      if (formData.singlePayPremium && formData.singlePayPaidInYear) {
        const singlePay = parseFloat(formData.singlePayPremium);
        const payYear = parseInt(formData.singlePayPaidInYear);
        if (year === payYear) {
          modifiedPremium = singlePay;
        } else {
          modifiedPremium = 0;
        }
        modifiedCashValue = singlePay * Math.pow(1.04, year - 1);
      }

      // Apply limited pay premium
      if (formData.limitedPayPremium && formData.limitedPayYears) {
        const limitedPremium = parseFloat(formData.limitedPayPremium);
        const limitedYears = parseInt(formData.limitedPayYears);
        modifiedPremium = year <= limitedYears ? limitedPremium : 0;
      }

      // Apply stop premium payments
      if (formData.stopPremiumPayments && formData.stopAge) {
        const stopAge = parseInt(formData.stopAge);
        const currentAge = 40; // Assume current age
        const stopYear = stopAge - currentAge;
        if (year >= stopYear) {
          modifiedPremium = 0;
        }
      }

      return {
        year,
        basePremium,
        modifiedPremium,
        baseCashValue,
        modifiedCashValue
      };
    });
  };

  // Run analysis
  const runAnalysis = () => {
    const projectionData = generateProjectionData();
    setAnalysisResults(projectionData);
  };

  // Generate report
  const generateReport = () => {
    runAnalysis();
    setShowReport(true);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      currentPremium: '',
      changePremium: false,
      singlePayPremium: '',
      singlePayPaidInYear: '',
      limitedPayPremium: '',
      limitedPayYears: '',
      changeYearToYear: false,
      continueScheduled: true,
      currentCreditingRate: '',
      guaranteedMinRate: '',
      excessEarningsRate: '',
      includeRiderChanges: false,
      stopPremiumPayments: false,
      stopAge: '',
      stopYear: '',
      stopOtherAge: '',
      stopCashValue: '',
      modelLevelPremium: false,
      levelPremiumPercentage: '',
      levelPremiumAmount: '',
      levelPremiumGoal: '',
      levelPremiumYears: '',
      levelPremiumAge: '',
      levelPremiumMaximize: false,
      levelPremiumEndAge: '',
      noLevelPremium: true,
      catchUpPremium: false,
      catchUpAmount: '',
      catchUpYears: '',
      catchUpOverYears: '',
      catchUpStartPolicyYear: '',
      catchUpStartAge: '',
      catchUpTarget: '',
      catchUpCashValue: '',
      noCatchUp: true
    });
    setAnalysisResults(null);
    setShowReport(false);
  };

  // --- Customer Info Extraction (similar to AsIsPage) ---
  let customerInfo = null;
  if (selectedCustomerData && selectedPolicyData) {
    // Extract premium amount from string (e.g., "2000 $ annually" -> "2000")
    const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
    const premiumAmount = premiumMatch ? premiumMatch[1] : '';
    // Extract coverage amount from string (e.g., "500,000 $" -> "500000")
    const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
    const coverageAmount = coverageMatch ? coverageMatch[1] : '';
    // Calculate current age from DOB (assuming DD.MM.YYYY)
    const calculateAge = (dobString: string): string => {
      const [day, month, year] = dobString.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age.toString();
    };
    customerInfo = {
      policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
      customerName: selectedCustomerData.name,
      customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
      policyType: selectedPolicyData.name,
      faceAmount: coverageAmount,
      annualPremium: premiumAmount,
      paymentPeriod: '20', // Default or extract if available
      dividendOption: 'Paid-up Additions', // Default or extract if available
      currentAge: calculateAge(selectedCustomerData.details.DOB),
      retirementAge: '65',
      lifeExpectancy: '85',
    };
  }

  // --- Render ---
  return (
    <div className="space-y-6">
      {/* Title (As-Is style) */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">PREMIUM ILLUSTRATION</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure premium scenarios for the selected policy.</p>
      </div>

      {/* Flow Indicator (optional, can match As-Is) */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              ✓
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">1. Policy Information</span>
          </div>
          <div className="w-8 h-1 bg-green-500"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2. Premium What-If</span>
          </div>
        </div>
      </div>

      {/* Show message if no policy is selected */}
      {!selectedCustomerData && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before running Premium What-If analysis.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Customer Information Card (As-Is style) */}
      {selectedCustomerData && selectedPolicyData && customerInfo && (
        <Card>
          <div className="flex items-center space-x-3 mb-6">
            <span className="w-6 h-6 text-blue-600"><Save /></span>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy Information</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Policy Number</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.policyNumber || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Customer Name</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.customerName || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Customer ID</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.customerId || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.policyType || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Face Amount ($)</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.faceAmount || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Annual Premium ($)</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.annualPremium || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Payment Period (Years)</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.paymentPeriod || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Dividend Option</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.dividendOption || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Current Age</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.currentAge || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Retirement Age</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.retirementAge || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Life Expectancy</p>
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{customerInfo?.lifeExpectancy || 'N/A'}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Main Form Sections (only if customer is selected) */}
      {selectedCustomerData && selectedPolicyData && (
        <div className="space-y-8">
          {/* Section 1: Premium Changes */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">1. Premium Changes</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Your current annual premium is:</label>
                <input
                  type="text"
                  value={formData.currentPremium}
                  onChange={(e) => handleInputChange('currentPremium', e.target.value)}
                  placeholder="Enter current premium amount"
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-lg font-semibold focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                />
              </div>

              <div>
                <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                  <input
                    type="checkbox"
                    checked={formData.changePremium}
                    onChange={(e) => handleCheckboxChange('changePremium', e.target.checked)}
                    className="w-5 h-5 text-blue-600"
                  />
                  <span>Do you want to change it?</span>
                </label>
              </div>

              {formData.changePremium && (
                <div className="ml-8 space-y-4 border-l-4 border-blue-300 pl-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Single pay (one-time premium): $</label>
                    <input
                      type="text"
                      value={formData.singlePayPremium}
                      onChange={(e) => handleInputChange('singlePayPremium', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                    {formData.singlePayPremium && (
                      <div className="mt-2">
                        <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">(Paid in Policy Year: )</label>
                        <input
                          type="text"
                          value={formData.singlePayPaidInYear}
                          onChange={(e) => handleInputChange('singlePayPaidInYear', e.target.value)}
                          className="w-32 px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Limited pay - pay premium for: $</label>
                    <input
                      type="text"
                      value={formData.limitedPayPremium}
                      onChange={(e) => handleInputChange('limitedPayPremium', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                    {formData.limitedPayPremium && (
                      <div className="mt-2 flex items-center space-x-2">
                        <input
                          type="text"
                          value={formData.limitedPayYears}
                          onChange={(e) => handleInputChange('limitedPayYears', e.target.value)}
                          className="w-20 px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                        />
                        <span className="text-sm font-medium text-gray-900 dark:text-gray-100">years</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Section 2: Premium Schedule */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">2. Premium Schedule</h3>

            <div className="space-y-4">
              <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                <input
                  type="checkbox"
                  checked={formData.changeYearToYear}
                  onChange={(e) => handleCheckboxChange('changeYearToYear', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>Yes, I want to change the premium by year-to-year premium schedule for each year</span>
              </label>

              <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                <input
                  type="checkbox"
                  checked={formData.continueScheduled}
                  onChange={(e) => handleCheckboxChange('continueScheduled', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>No, continue scheduled premium payments</span>
              </label>
            </div>
          </div>

          {/* Section 3: Interest Rate Assumptions */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">3. Interest Rate Assumptions</h3>

            <div className="mb-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">Run projections under the following interest rate assumptions:</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-black font-semibold mb-2">Current crediting rate: %</label>
                <input
                  type="text"
                  value={formData.currentCreditingRate}
                  onChange={(e) => handleInputChange('currentCreditingRate', e.target.value)}
                  className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                />
              </div>

              <div>
                <label className="block text-black font-semibold mb-2">Guaranteed minimum rate: %</label>
                <input
                  type="text"
                  value={formData.guaranteedMinRate}
                  onChange={(e) => handleInputChange('guaranteedMinRate', e.target.value)}
                  className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                />
              </div>

              <div>
                <label className="block text-black font-semibold mb-2">Excess earnings rate: %</label>
                <input
                  type="text"
                  value={formData.excessEarningsRate}
                  onChange={(e) => handleInputChange('excessEarningsRate', e.target.value)}
                  className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                <input
                  type="checkbox"
                  checked={formData.includeRiderChanges}
                  onChange={(e) => handleCheckboxChange('includeRiderChanges', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>Do you want to include rider changes in this scenario?</span>
              </label>
            </div>
          </div>

          {/* Section 4: Stop Premium Payments */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">4. Stop Premium Payments</h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">Do you want to stop paying future premiums and see how long the policy remains in force? Or Do you want model policy lapse survival by stopping premiums?</p>
            </div>

            <div className="space-y-4">
              <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                <input
                  type="checkbox"
                  checked={formData.stopPremiumPayments}
                  onChange={(e) => handleCheckboxChange('stopPremiumPayments', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>Yes, stop all premium payments starting:</span>
              </label>

              {formData.stopPremiumPayments && (
                <div className="ml-8 space-y-4 border-l-4 border-blue-300 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Age:</label>
                      <input
                        type="text"
                        value={formData.stopAge}
                        onChange={(e) => handleInputChange('stopAge', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Year:</label>
                      <input
                        type="text"
                        value={formData.stopYear}
                        onChange={(e) => handleInputChange('stopYear', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Other - Age:</label>
                      <input
                        type="text"
                        value={formData.stopOtherAge}
                        onChange={(e) => handleInputChange('stopOtherAge', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Based on condition (e.g., cash value reaches $):</label>
                    <input
                      type="text"
                      value={formData.stopCashValue}
                      onChange={(e) => handleInputChange('stopCashValue', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Section 5: Level Premium Payments */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">5. Level Premium Payment Projections</h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">Do you want to model level premium payment projections?</p>
            </div>

            <div className="space-y-4">
              <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                <input
                  type="checkbox"
                  checked={formData.modelLevelPremium}
                  onChange={(e) => handleCheckboxChange('modelLevelPremium', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>Yes, project a fixed level premium of</span>
              </label>

              {formData.modelLevelPremium && (
                <div className="ml-8 space-y-4 border-l-4 border-blue-300 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">$ amount:</label>
                      <input
                        type="text"
                        value={formData.levelPremiumAmount}
                        onChange={(e) => handleInputChange('levelPremiumAmount', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">% annually:</label>
                      <input
                        type="text"
                        value={formData.levelPremiumPercentage}
                        onChange={(e) => handleInputChange('levelPremiumPercentage', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Increase premium for specific goal:</label>
                      <input
                        type="text"
                        value={formData.levelPremiumGoal}
                        onChange={(e) => handleInputChange('levelPremiumGoal', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-black font-semibold mb-2">Increase premium by years:</label>
                      <input
                        type="text"
                        value={formData.levelPremiumYears}
                        onChange={(e) => handleInputChange('levelPremiumYears', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-black font-semibold mb-2">Age/Policy Year:</label>
                    <input
                      type="text"
                      value={formData.levelPremiumAge}
                      onChange={(e) => handleInputChange('levelPremiumAge', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                  </div>

                  <div>
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={formData.levelPremiumMaximize}
                        onChange={(e) => handleCheckboxChange('levelPremiumMaximize', e.target.checked)}
                        className="w-5 h-5 text-blue-600"
                      />
                      <span>Maximize cash value by Age (Policy year):</span>
                    </label>
                    {formData.levelPremiumMaximize && (
                      <div className="ml-8 mt-2">
                        <input
                          type="text"
                          value={formData.levelPremiumEndAge}
                          onChange={(e) => handleInputChange('levelPremiumEndAge', e.target.value)}
                          className="w-32 px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                <input
                  type="checkbox"
                  checked={formData.noLevelPremium}
                  onChange={(e) => handleCheckboxChange('noLevelPremium', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>No, do not model level premium payments</span>
              </label>
            </div>
          </div>

          {/* Section 6: Catch-up Premium */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 pb-3 border-b-2 border-gray-200">6. Catch-up Premium Analysis</h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">Do you want to pay catch-up premiums to restore or strengthen your policy's performance?</p>
            </div>

            <div className="space-y-4">
              <label className="flex items-center space-x-3 text-sm font-medium text-gray-900 dark:text-gray-100">
                <input
                  type="checkbox"
                  checked={formData.catchUpPremium}
                  onChange={(e) => handleCheckboxChange('catchUpPremium', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>Yes, pay catch-up premium amount</span>
              </label>

              {formData.catchUpPremium && (
                <div className="ml-8 space-y-4 border-l-4 border-blue-300 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">$ Lump sum catch-up premium: $</label>
                      <input
                        type="text"
                        value={formData.catchUpAmount}
                        onChange={(e) => handleInputChange('catchUpAmount', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Spread catch-up premium over years:</label>
                      <input
                        type="text"
                        value={formData.catchUpYears}
                        onChange={(e) => handleInputChange('catchUpYears', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">% over next years:</label>
                      <input
                        type="text"
                        value={formData.catchUpOverYears}
                        onChange={(e) => handleInputChange('catchUpOverYears', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Starting in Policy Year:</label>
                      <input
                        type="text"
                        value={formData.catchUpStartPolicyYear}
                        onChange={(e) => handleInputChange('catchUpStartPolicyYear', e.target.value)}
                        className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-black font-semibold mb-2">Starting when policyholder is age:</label>
                    <input
                      type="text"
                      value={formData.catchUpStartAge}
                      onChange={(e) => handleInputChange('catchUpStartAge', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                  </div>

                  <div>
                    <label className="block text-black font-semibold mb-2">Based on condition (e.g., cash value below $):</label>
                    <input
                      type="text"
                      value={formData.catchUpTarget}
                      onChange={(e) => handleInputChange('catchUpTarget', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                  </div>

                  <div>
                    <label className="block text-black font-semibold mb-2">(IDB target, etc.):</label>
                    <input
                      type="text"
                      value={formData.catchUpCashValue}
                      onChange={(e) => handleInputChange('catchUpCashValue', e.target.value)}
                      className="w-full px-4 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-blue-50 text-black"
                    />
                  </div>
                </div>
              )}

              <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                <input
                  type="checkbox"
                  checked={formData.noCatchUp}
                  onChange={(e) => handleCheckboxChange('noCatchUp', e.target.checked)}
                  className="w-5 h-5 text-blue-600"
                />
                <span>No, do not illustrate catch-up premium scenarios</span>
              </label>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons (bottom, As-Is style) */}
      {selectedCustomerData && selectedPolicyData && (
        <div className="flex flex-wrap gap-4 justify-center mt-8">
          <Button
            onClick={() => alert('Analysis saved to history!')}
            variant="primary"
            className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
          >
            <Save className="w-4 h-4" />
            <span>Save Premium Analysis</span>
          </Button>
          <Button
            onClick={resetForm}
            variant="primary"
            className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
          >
            <span>Reset</span>
          </Button>
          <Button
            onClick={() => setActiveTab('face-amount')}
            className="flex items-center space-x-2"
          >
            <span>Proceed to Income Illustration</span>
            <ArrowRight className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Analysis Results */}
      {analysisResults && (
        <div className="mt-8 bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl shadow-lg border-l-4 border-green-500">
          <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6 text-center">📊 Premium Analysis Results</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Premium Projection Chart */}
            <div>
              <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">Premium Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="basePremium" stroke="#2563eb" strokeWidth={2} name="Current Premium" />
                  <Line type="monotone" dataKey="modifiedPremium" stroke="#dc2626" strokeWidth={2} name="Modified Premium" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Cash Value Projection Chart */}
            <div>
              <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">Cash Value Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="baseCashValue" stroke="#16a34a" strokeWidth={2} name="Current Cash Value" />
                  <Line type="monotone" dataKey="modifiedCashValue" stroke="#ea580c" strokeWidth={2} name="Modified Cash Value" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Analysis Summary */}
          <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
            <h4 className="text-md font-semibold text-gray-900 dark:text-gray-100 mb-4">Analysis Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {formData.currentPremium && (
                <div className="text-black">
                  <strong>Current Annual Premium:</strong> ${formData.currentPremium}
                </div>
              )}
              {formData.singlePayPremium && (
                <div className="text-black">
                  <strong>Single Pay Premium:</strong> ${formData.singlePayPremium}
                </div>
              )}
              {formData.limitedPayPremium && (
                <div className="text-black">
                  <strong>Limited Pay Premium:</strong> ${formData.limitedPayPremium} for {formData.limitedPayYears} years
                </div>
              )}
              {formData.currentCreditingRate && (
                <div className="text-black">
                  <strong>Current Crediting Rate:</strong> {formData.currentCreditingRate}%
                </div>
              )}
              {formData.guaranteedMinRate && (
                <div className="text-black">
                  <strong>Guaranteed Min Rate:</strong> {formData.guaranteedMinRate}%
                </div>
              )}
              {formData.stopPremiumPayments && formData.stopAge && (
                <div className="text-black">
                  <strong>Stop Premium at Age:</strong> {formData.stopAge}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Report */}
      {showReport && (
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
          <h3 className="text-xl font-bold text-blue-800 mb-6 text-center">📈 Comprehensive Premium Analysis Report</h3>
          
          <div className="space-y-6">
            {/* Report Header */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Report Details</h4>
              <p><strong>Report Generated:</strong> {new Date().toLocaleString()}</p>
              <p><strong>Customer:</strong> {selectedCustomerData?.name || 'N/A'}</p>
              <p><strong>Policy Number:</strong> {selectedCustomerData?.details?.["Policy Number"] || selectedCustomerData?.policyNumber || 'N/A'}</p>
              <p><strong>Policy Type:</strong> {selectedPolicyData?.name || 'N/A'}</p>
            </div>

            {/* Executive Summary */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Executive Summary</h4>
              <ul className="space-y-2 text-black">
                {formData.currentPremium && <li>• Current annual premium: ${formData.currentPremium}</li>}
                {formData.singlePayPremium && <li>• Proposed single pay premium: ${formData.singlePayPremium}</li>}
                {formData.limitedPayPremium && <li>• Proposed limited pay premium: ${formData.limitedPayPremium} for {formData.limitedPayYears} years</li>}
                {formData.stopPremiumPayments && <li>• Analysis includes premium payment cessation scenario</li>}
                {formData.modelLevelPremium && <li>• Level premium payment projections included</li>}
                {formData.catchUpPremium && <li>• Catch-up premium analysis included</li>}
              </ul>
            </div>

            {/* Recommendations */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Recommendations</h4>
              <ul className="space-y-2 text-black">
                {formData.stopPremiumPayments && <li>• Consider the policy lapse risk when stopping premium payments</li>}
                {formData.singlePayPremium && <li>• Single pay option eliminates future premium payment uncertainty</li>}
                {formData.catchUpPremium && <li>• Catch-up premiums can help restore policy performance</li>}
                {parseFloat(formData.currentCreditingRate) < 4.0 && <li>• Consider policy review due to low crediting rates</li>}
                <li>• Regular policy reviews are recommended to monitor performance</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumAnalysisForm;