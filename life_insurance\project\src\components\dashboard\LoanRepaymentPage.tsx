import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { Save, RotateCcw, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import CurrentPolicyInfoCard from '../common/CurrentPolicyInfoCard';

const LoanRepaymentPage: React.FC = () => {
  const { setActiveTab, selectedCustomerData, selectedPolicyData } = useDashboard();
  // Section 1: Loan Repayment Method
  const [repaymentEnabled, setRepaymentEnabled] = useState(true);
  const [repaymentType, setRepaymentType] = useState('scheduled');
  const [scheduledRepayment, setScheduledRepayment] = useState({
    fixedAmount: '',
    duration: '',
    startType: '',
    startValue: '',
    afterRetirement: false,
    flexibleTiming: false,
    accelerated: false,
    acceleratedYears: '',
    testSkipped: false,
    skippedYears: '',
  });
  const [lumpSum, setLumpSum] = useState({
    amount: '',
    year: '',
    atRetirement: false,
    triggerEvent: false,
  });
  const [interestOnly, setInterestOnly] = useState({
    annualInterest: '',
    duration: '',
    immediate: false,
    flexibleTiming: false,
    interestOnlyInitial: false,
  });
  // Section 2: Repayment by Reducing Death Benefit
  const [reduceDBEnabled, setReduceDBEnabled] = useState(false);
  const [reduceDB, setReduceDB] = useState<{
    startType: string;
    startValue: string;
    targetType: string;
    targetValue: string;
    savingsUsage: string;
    compliance: string[];
  }>({
    startType: '',
    startValue: '',
    targetType: '',
    targetValue: '',
    savingsUsage: '',
    compliance: [],
  });

  // Placeholder for schedule button
  const handleSchedule = (section: string) => {
    alert(`Open schedule editor for: ${section}`);
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    setRepaymentEnabled(true);
    setRepaymentType('scheduled');
    setScheduledRepayment({
      fixedAmount: '',
      duration: '',
      startType: '',
      startValue: '',
      afterRetirement: false,
      flexibleTiming: false,
      accelerated: false,
      acceleratedYears: '',
      testSkipped: false,
      skippedYears: '',
    });
    setLumpSum({
      amount: '',
      year: '',
      atRetirement: false,
      triggerEvent: false,
    });
    setInterestOnly({
      annualInterest: '',
      duration: '',
      immediate: false,
      flexibleTiming: false,
      interestOnlyInitial: false,
    });
    setReduceDBEnabled(false);
    setReduceDB({
      startType: '',
      startValue: '',
      targetType: '',
      targetValue: '',
      savingsUsage: '',
      compliance: [],
    });
    alert('All loan repayment scenarios have been reset!');
  };

  // Interest/compounding/distribution/tax/source box
  const InterestBox = () => (
    <div className="p-4 border rounded-lg bg-gray-50 mt-4">
      <div className="font-semibold mb-2 text-black">Run projections under the following interest rate assumptions:</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
        <Input label="Current crediting rate" placeholder="%" className="text-black placeholder-black" />
        <Input label="Guaranteed minimum rate" placeholder="%" className="text-black placeholder-black" />
        <Input label="Stress scenario rate" placeholder="%" className="text-black placeholder-black" />
      </div>
      <div className="font-semibold mb-2 text-black">Loan interest rate assumptions:</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
        <Input label="Fixed Interest rate" placeholder="%" className="text-black placeholder-black" />
        <Input label="Variable loan rate projection (specify range)" placeholder="% to %" className="text-black placeholder-black" />
      </div>
      <div className="font-semibold mb-2 text-black">Compounding method:</div>
      <Select label="Compounding" value={''} onChange={() => {}} options={[{ value: 'simple', label: 'Simple' }, { value: 'compounded', label: 'Compounded annually' }]} className="text-black" />
      <div className="font-semibold mb-2 text-black mt-4">How are distributions applied toward repayment?</div>
      <Select label="Distribution" value={''} onChange={() => {}} options={[
        { value: 'interest-first', label: 'Interest-only first, then principal' },
        { value: 'even-split', label: 'Evenly split between interest and principal' },
        { value: 'lump-sum', label: 'Lump sum from cumulative distributions' },
      ]} className="text-black" />
      <div className="font-semibold mb-2 text-black mt-4">Tax considerations (if applicable)</div>
      <Select label="Tax" value={''} onChange={() => {}} options={[
        { value: 'non-mec', label: 'Will policy remain non-MEC?' },
        { value: 'fifo', label: 'Assume FIFO tax treatment' },
        { value: 'income-tax', label: 'Model income tax if basis is exceeded' },
      ]} className="text-black" />
      <div className="font-semibold mb-2 text-black mt-4">Source of repayment</div>
      <Select label="Source" value={''} onChange={() => {}} options={[
        { value: 'new-premium', label: 'New/additional premium contributions/Out-of-pocket (external funds)' },
        { value: 'policy-values', label: 'Repay using policy values (Repaid from the policy)' },
        { value: 'future-premium', label: 'Premium offset (loan repaid with future premium stream)' },
      ]} className="text-black" />
    </div>
  );

  // Navigation handler
  const handleProceedToLoanRepayment = () => {
    setActiveTab('loan-repayment-illustration');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">LOAN REPAYMENT</h1>
      </div>
      {/* Stepper */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              ✓
            </div>
            <span className="text-sm font-medium text-green-600 dark:text-green-400">1. Policy Information</span>
          </div>
          <div className="w-8 h-1 bg-green-500"></div>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2. Loan Repayment Scenarios</span>
          </div>
        </div>
      </div>
      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Loan Repayment illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          <CurrentPolicyInfoCard selectedCustomerData={selectedCustomerData} selectedPolicyData={selectedPolicyData} />
          {/* Section 1: Loan Repayment Method */}
          <Card className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">1. Do you want to model a loan repayment strategy (scheduled or lump sum)?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={repaymentEnabled} onChange={e => setRepaymentEnabled(e.target.checked)} />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Yes, model a loan repayment method through repayment</span>
              </div>
              {repaymentEnabled && (
                <div className="space-y-6 pl-6">
                  {/* Scheduled repayment */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <input type="radio" checked={repaymentType === 'scheduled'} onChange={() => setRepaymentType('scheduled')} />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Scheduled repayment</span>
                    </div>
                    {repaymentType === 'scheduled' && (
                      <div className="pl-6 space-y-2">
                        <div className="flex items-center space-x-2">
                          <Input label="Fixed annual (level) payment" value={scheduledRepayment.fixedAmount} onChange={e => setScheduledRepayment(prev => ({ ...prev, fixedAmount: e.target.value }))} className="text-black placeholder-black" />
                          <Input label="Duration (years or until Policy Year)" value={scheduledRepayment.duration} onChange={e => setScheduledRepayment(prev => ({ ...prev, duration: e.target.value }))} className="text-black placeholder-black" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="radio" checked={scheduledRepayment.startType === 'now'} onChange={() => setScheduledRepayment(prev => ({ ...prev, startType: 'now', startValue: '' }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Starting now</span>
                          <input type="radio" checked={scheduledRepayment.startType === 'future'} onChange={() => setScheduledRepayment(prev => ({ ...prev, startType: 'future', startValue: '' }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Starting in future Policy Year/Age</span>
                          <Input value={scheduledRepayment.startType === 'future' ? scheduledRepayment.startValue : ''} onChange={e => setScheduledRepayment(prev => ({ ...prev, startValue: e.target.value }))} className="text-black placeholder-black w-32" placeholder="Year/Age" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={scheduledRepayment.afterRetirement} onChange={e => setScheduledRepayment(prev => ({ ...prev, afterRetirement: e.target.checked }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">After retirement / income phase starts (e.g., age 65)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={scheduledRepayment.flexibleTiming} onChange={e => setScheduledRepayment(prev => ({ ...prev, flexibleTiming: e.target.checked }))} />
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Flexible repayment timing and frequency</span>
                          <Button onClick={() => handleSchedule('Flexible Repayment Timing')} className="ml-2">Schedule</Button>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={scheduledRepayment.accelerated} onChange={e => setScheduledRepayment(prev => ({ ...prev, accelerated: e.target.checked }))} />
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Accelerated loan repayment schedule</span>
                          <span className="text-sm text-gray-700 dark:text-gray-300">Pay off the entire loan within</span>
                          <Input value={scheduledRepayment.acceleratedYears} onChange={e => setScheduledRepayment(prev => ({ ...prev, acceleratedYears: e.target.value }))} className="text-black placeholder-black w-20" placeholder="years" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={scheduledRepayment.testSkipped} onChange={e => setScheduledRepayment(prev => ({ ...prev, testSkipped: e.target.checked }))} />
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Test skipped or missed payments- skip year(s):</span>
                          <Input value={scheduledRepayment.skippedYears} onChange={e => setScheduledRepayment(prev => ({ ...prev, skippedYears: e.target.value }))} className="text-black placeholder-black w-20" placeholder="years" />
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Lump sum repayment */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <input type="radio" checked={repaymentType === 'lump'} onChange={() => setRepaymentType('lump')} />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Lump sum repayment</span>
                    </div>
                    {repaymentType === 'lump' && (
                      <div className="pl-6 space-y-2">
                        <div className="flex items-center space-x-2">
                          <Input label="Amount" value={lumpSum.amount} onChange={e => setLumpSum(prev => ({ ...prev, amount: e.target.value }))} className="text-black placeholder-black" />
                          <Input label="Repayment year: Policy Year/Age" value={lumpSum.year} onChange={e => setLumpSum(prev => ({ ...prev, year: e.target.value }))} className="text-black placeholder-black" />
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={lumpSum.atRetirement} onChange={e => setLumpSum(prev => ({ ...prev, atRetirement: e.target.checked }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">At retirement age (e.g., 60 or 65)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={lumpSum.triggerEvent} onChange={e => setLumpSum(prev => ({ ...prev, triggerEvent: e.target.checked }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Upon triggering event (e.g., sale of asset, inheritance)</span>
                        </div>
                      </div>
                    )}
                  </div>
                  {/* Interest only payment */}
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <input type="radio" checked={repaymentType === 'interest'} onChange={() => setRepaymentType('interest')} />
                      <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Interest only Payment</span>
                    </div>
                    {repaymentType === 'interest' && (
                      <div className="pl-6 space-y-2">
                        <Input label="Annual interest payment" value={interestOnly.annualInterest} onChange={e => setInterestOnly(prev => ({ ...prev, annualInterest: e.target.value }))} className="text-black placeholder-black" />
                        <div className="flex items-center space-x-2">
                          <Input label="Duration" value={interestOnly.duration} onChange={e => setInterestOnly(prev => ({ ...prev, duration: e.target.value }))} className="text-black placeholder-black w-32" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">years starting Policy Year/Age</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={interestOnly.immediate} onChange={e => setInterestOnly(prev => ({ ...prev, immediate: e.target.checked }))} />
                          <span className="text-sm text-gray-700 dark:text-gray-300">Immediately after the loan is taken</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={interestOnly.flexibleTiming} onChange={e => setInterestOnly(prev => ({ ...prev, flexibleTiming: e.target.checked }))} />
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Flexible interest only timing and frequency</span>
                          <Button onClick={() => handleSchedule('Flexible Interest Only Timing')} className="ml-2">Schedule</Button>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input type="checkbox" checked={interestOnly.interestOnlyInitial} onChange={e => setInterestOnly(prev => ({ ...prev, interestOnlyInitial: e.target.checked }))} />
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Interest-only for initial years, then fully amortized payments</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!repaymentEnabled} onChange={e => setRepaymentEnabled(!e.target.checked)} />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">No, do not illustrate loan repayment scenario.</span>
              </div>
              <InterestBox />
            </div>
          </Card>
          {/* Section 2: Repayment by Reducing Death Benefit */}
          <Card className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">2. Do you want to model loan repayment by reducing the death benefit (face amount)?</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={reduceDBEnabled} onChange={e => setReduceDBEnabled(e.target.checked)} />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Yes, model loan repayment by reducing the death benefit</span>
              </div>
              {reduceDBEnabled && (
                <div className="space-y-4 pl-6">
                  <div className="text-md font-semibold text-gray-900 dark:text-gray-100">Starting</div>
                  <div className="flex items-center space-x-2">
                    <input type="radio" checked={reduceDB.startType === 'immediate'} onChange={() => setReduceDB(prev => ({ ...prev, startType: 'immediate' }))} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Immediately</span>
                    <input type="radio" checked={reduceDB.startType === 'policy-year'} onChange={() => setReduceDB(prev => ({ ...prev, startType: 'policy-year' }))} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">At policy year/Age:</span>
                    <Input value={reduceDB.startType === 'policy-year' ? reduceDB.startValue : ''} onChange={e => setReduceDB(prev => ({ ...prev, startValue: e.target.value }))} className="text-black placeholder-black w-32" placeholder="Year/Age" />
                  </div>
                  <div className="text-md font-semibold text-gray-900 dark:text-gray-100 mt-4">What is the target death benefit after reduction?</div>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={reduceDB.targetType === 'min-mec'} onChange={() => setReduceDB(prev => ({ ...prev, targetType: 'min-mec' }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Reduce to minimum non-MEC face amount</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={reduceDB.targetType === 'reduce-to'} onChange={() => setReduceDB(prev => ({ ...prev, targetType: 'reduce-to' }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Reduce to $</span>
                      <Input value={reduceDB.targetType === 'reduce-to' ? reduceDB.targetValue : ''} onChange={e => setReduceDB(prev => ({ ...prev, targetValue: e.target.value }))} className="text-black placeholder-black w-32" placeholder="$" />
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={reduceDB.targetType === 'guideline'} onChange={() => setReduceDB(prev => ({ ...prev, targetType: 'guideline' }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Use guideline/target premium to recalculate</span>
                    </div>
                  </div>
                  <div className="text-md font-semibold text-gray-900 dark:text-gray-100 mt-4">How should the savings from reduced COI (Cost of Insurance) be used?</div>
                  <Select label="Savings Usage" value={reduceDB.savingsUsage} onChange={e => setReduceDB(prev => ({ ...prev, savingsUsage: e.target.value }))} options={[
                    { value: '', label: 'Select...' },
                    { value: 'principal', label: 'Redirect savings to repay loan principal' },
                    { value: 'interest', label: 'Redirect to pay loan interest' },
                    { value: 'build-cv', label: 'Redirect to build cash value and repay loan later' },
                  ]} className="text-black" />
                  <div className="text-md font-semibold text-gray-900 dark:text-gray-100 mt-4">Policy compliance assumptions</div>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" checked={reduceDB.compliance.includes('non-mec')} onChange={e => setReduceDB(prev => ({ ...prev, compliance: e.target.checked ? [...prev.compliance, 'non-mec'] : prev.compliance.filter(c => c !== 'non-mec') }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Maintain policy as non-MEC</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" checked={reduceDB.compliance.includes('guideline')} onChange={e => setReduceDB(prev => ({ ...prev, compliance: e.target.checked ? [...prev.compliance, 'guideline'] : prev.compliance.filter(c => c !== 'guideline') }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Re-test for guideline premium limits</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" checked={reduceDB.compliance.includes('corridor')} onChange={e => setReduceDB(prev => ({ ...prev, compliance: e.target.checked ? [...prev.compliance, 'corridor'] : prev.compliance.filter(c => c !== 'corridor') }))} />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Comply with corridor requirements (if UL or IUL)</span>
                    </div>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!reduceDBEnabled} onChange={e => setReduceDBEnabled(!e.target.checked)} />
                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">No, do not model repayment via reduced death benefit.</span>
              </div>
            </div>
          </Card>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={() => alert('Loan repayment scenario saved!')}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Loan Repayment Illustration</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={handleProceedToLoanRepayment}
              className="flex items-center space-x-2"
            >
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default LoanRepaymentPage;